import { useAsync } from '@topwrite/common';
import { useCallback } from 'react';
import { socket } from '../../lib/socket';

interface McpServer {
    name: string;
    url: string;
}

interface McpConfig {
    servers: McpServer[];
}

export default function useMcpServers() {
    const { result: mcp = { servers: [] } } = useAsync(async () => {
        const buf = await socket.readFile('.topwrite/mcp.json');
        let mcp: McpConfig;
        try {
            mcp = JSON.parse(buf.toString());
        } catch {
            mcp = { servers: [] };
        }
        return mcp;
    }, []);

    const { servers = [] } = mcp;

    const addServer = useCallback((server: McpServer) => {
    }, []);

    const deleteServer = useCallback((name: string) => {

    }, []);


    return { servers, addServer, deleteServer };
}
