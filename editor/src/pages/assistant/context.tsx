import { isCancel, isRequestError, request, RequestConfig, useOptions, useSelector } from '@topwrite/common';
import {
    createContext,
    Dispatch,
    PropsWithChildren,
    SetStateAction,
    useCallback,
    useContext as useRc<PERSON>ontext,
    useEffect, useMemo,
    useRef,
    useState
} from 'react';
import useImmer, { Updater } from '../../lib/use-immer';
import { Message } from './message-item';
import {
    BuiltinTool,
    McpServer,
    getEnabledToolsStatus
} from '../../lib/tools-config';
import useLocalStorageStateWithBook from '../../lib/use-local-storage-state-with-book';
import useMcpServers from './use-mcp-servers';

export interface Conversation {
    id: string;
    title?: string;
    create_time: string;
    messages: Message[];
}

export type WindowState = 'chat' | 'history' | 'settings';

interface ContextType {
    filename: string | null;
    range?: FileRange | null;
    conversationId?: string;
    setConversationId: Dispatch<SetStateAction<string | undefined>>;
    messages: Message[];
    setMessages: Updater<Message[]>;
    loading: boolean;
    setLoading: Dispatch<SetStateAction<boolean>>;
    send: (query: string) => Promise<void>;
    stop: () => void;
    reset: (conversation?: Conversation) => void;
    windowState: WindowState;
    setWindowState: (state: WindowState) => void;
    authorized: string | boolean;
    // 工具配置相关
    builtinTools: BuiltinTool[];
    mcpServers: McpServer[];
    toggleBuiltinTool: (toolName: string) => void;
    toggleAllBuiltinTools: (enabled: boolean) => void;
    toggleMcpServer: (serverName: string) => void;
    toggleMcpTool: (serverName: string, toolName: string) => void;
    toggleMcpServerTools: (serverName: string, enabled: boolean) => void;
    addMcpServer: (serverName: string, serverUrl: string) => Promise<void>;
    deleteMcpServer: (serverName: string) => Promise<void>;
    saveToolsConfig: () => Promise<void>;
    loadToolsConfig: () => Promise<void>;
}

const Context = createContext<ContextType | null>(null);

export function Provider({ children, conversation }: PropsWithChildren<{ conversation?: Conversation }>) {
    const { assistant } = useOptions();
    const { current: filename } = useSelector('workspace');
    const { config } = useSelector('book');
    const [windowState, setWindowState] = useState<WindowState>('chat');

    const authorized = useMemo<string | boolean>(() => {
        if (assistant.authorized) {
            return true;
        }
        return config.getValue(['assistant', 'token']) || false;
    }, [assistant.authorized, config]);

    const getInitialMessages = useCallback((conversation?: Conversation) => {
        const messages: Message[] = [];

        if (conversation) {
            return messages.concat(conversation.messages);
        }
        return messages;
    }, []);

    const [conversationId, setConversationId] = useState<string | undefined>(conversation?.id);
    const conversationRef = useRef(conversationId);

    useEffect(() => {
        conversationRef.current = conversationId;
    }, [conversationId]);

    const [messages, setMessages] = useImmer<Message[]>(() => {
        return getInitialMessages(conversation);
    });

    const controller = useRef<AbortController | null>(null);
    const [loading, setLoading] = useState(false);

    // 工具配置相关状态
    const [builtinTools, setBuiltinTools] = useState<BuiltinTool[]>([]);
    const [mcpServers, setMcpServers] = useState<McpServer[]>([]);

    // 使用 useMcpServers hook 管理 MCP 服务器
    const { servers: mcpServerConfigs, addServer: addMcpServerConfig, deleteServer: deleteMcpServerConfig } = useMcpServers();

    // 使用 useLocalStorageStateWithBook 管理工具配置
    const [builtinToolsConfig, setBuiltinToolsConfig] = useLocalStorageStateWithBook<Record<string, boolean>>('builtin-tools-config', {});
    const [mcpToolsConfig, setMcpToolsConfig] = useLocalStorageStateWithBook<Record<string, Record<string, boolean>>>('mcp-tools-config', {});

    // 加载工具配置
    const loadToolsConfig = useCallback(async () => {
        try {
            // 从 assistant.tools 获取内置工具配置
            const assistantTools = assistant.tools || [];
            const updatedBuiltinTools = assistantTools.map(tool => ({
                ...tool,
                enabled: builtinToolsConfig[tool.name] !== undefined ? builtinToolsConfig[tool.name] : true
            }));
            setBuiltinTools(updatedBuiltinTools);

            // 从服务端加载 MCP 服务器列表
            let mcpServersData: Array<{
                name: string;
                url?: string;
                tools: Array<{ name: string; title: string; description: string }>
            }> = [];

            try {
                const requestConfig: RequestConfig = {
                    method: 'get',
                    url: `${assistant.base}/assistant/mcp/tools`
                };

                if (typeof authorized === 'string') {
                    requestConfig.params = { token: authorized };
                }

                const response = await request(requestConfig);
                mcpServersData = response.data?.servers || [];
            } catch (error) {
                console.warn('Failed to load MCP servers from server, falling back to local config:', error);
                // 如果服务端加载失败，回退到本地配置文件
                mcpServersData = mcpServerConfigs.map(config => ({
                    name: config.name,
                    url: config.url,
                    tools: []
                }));
            }

            const updatedMcpServers = mcpServersData.map(server => ({
                name: server.name,
                url: server.url,
                tools: server.tools.map(tool => ({
                    ...tool,
                    enabled: mcpToolsConfig[server.name]?.[tool.name] !== undefined
                        ? mcpToolsConfig[server.name][tool.name]
                        : false
                })),
                expanded: false,
                enabledCount: server.tools.filter(tool =>
                    mcpToolsConfig[server.name]?.[tool.name] !== undefined
                        ? mcpToolsConfig[server.name][tool.name]
                        : false
                ).length
            }));

            setMcpServers(updatedMcpServers);
        } catch (error) {
            console.error('Failed to load tools configuration:', error);
        }
    }, [assistant.tools, assistant.base, authorized, builtinToolsConfig, mcpToolsConfig, mcpServerConfigs]);

    // 初始化时加载工具配置
    useEffect(() => {
        loadToolsConfig();
    }, [loadToolsConfig]);

    // 保存工具配置
    const saveToolsConfig = useCallback(async () => {
        try {
            // 保存内置工具配置到 localStorage
            const builtinConfig: Record<string, boolean> = {};
            builtinTools.forEach(tool => {
                builtinConfig[tool.name] = tool.enabled;
            });
            setBuiltinToolsConfig(builtinConfig);

            // 保存 MCP 工具配置到 localStorage
            const mcpConfig: Record<string, Record<string, boolean>> = {};
            mcpServers.forEach(server => {
                mcpConfig[server.name] = {};
                server.tools.forEach(tool => {
                    mcpConfig[server.name][tool.name] = tool.enabled;
                });
            });
            setMcpToolsConfig(mcpConfig);
        } catch (error) {
            console.error('Failed to save tools configuration:', error);
        }
    }, [builtinTools, mcpServers, setBuiltinToolsConfig, setMcpToolsConfig]);

    // 切换内置工具开关
    const toggleBuiltinTool = useCallback((toolName: string) => {
        setBuiltinTools(prev => prev.map(tool =>
            tool.name === toolName
                ? { ...tool, enabled: !tool.enabled }
                : tool
        ));
    }, []);

    // 批量开启/关闭内置工具
    const toggleAllBuiltinTools = useCallback((enabled: boolean) => {
        setBuiltinTools(prev => prev.map(tool => ({ ...tool, enabled })));
    }, []);

    // 切换 MCP 服务器展开/折叠
    const toggleMcpServer = useCallback(async (serverName: string) => {
        const currentServer = mcpServers.find(server => server.name === serverName);

        // 如果是首次展开且工具列表为空，则通过 POST 接口获取工具列表
        if (currentServer && !currentServer.expanded && currentServer.tools.length === 0) {
            try {
                const requestData = {
                    name: serverName,
                    url: currentServer.url
                };

                const requestConfig: RequestConfig = {
                    method: 'post',
                    url: `${assistant.base}/assistant/mcp/tools`,
                    data: requestData
                };

                if (typeof authorized === 'string') {
                    requestConfig.data.token = authorized;
                }

                const response = await request(requestConfig);
                const tools = response.data?.tools || [];

                if (tools.length > 0) {
                    // 更新服务器的工具列表
                    setMcpServers(prev => prev.map(server => {
                        if (server.name === serverName) {
                            const updatedTools = tools.map((tool: any) => ({
                                ...tool,
                                enabled: mcpToolsConfig[serverName]?.[tool.name] !== undefined
                                    ? mcpToolsConfig[serverName][tool.name]
                                    : false
                            }));
                            return {
                                ...server,
                                tools: updatedTools,
                                expanded: true,
                                enabledCount: updatedTools.filter((t: any) => t.enabled).length
                            };
                        }
                        return server;
                    }));
                    return; // 已经设置了展开状态，直接返回
                }
            } catch (error) {
                console.error('Failed to load tools for MCP server:', serverName, error);
                // 加载失败时仍然可以展开，只是工具列表为空
            }
        }

        // 正常的展开/折叠切换
        setMcpServers(prev => prev.map(server =>
            server.name === serverName
                ? { ...server, expanded: !server.expanded }
                : server
        ));
    }, [mcpServers, assistant.base, authorized, mcpToolsConfig]);

    // 切换 MCP 工具开关
    const toggleMcpTool = useCallback((serverName: string, toolName: string) => {
        setMcpServers(prev => prev.map(server => {
            if (server.name === serverName) {
                const updatedTools = server.tools.map(tool =>
                    tool.name === toolName
                        ? { ...tool, enabled: !tool.enabled }
                        : tool
                );
                return {
                    ...server,
                    tools: updatedTools,
                    enabledCount: updatedTools.filter(t => t.enabled).length
                };
            }
            return server;
        }));
    }, []);

    // 批量开启/关闭 MCP 服务器内所有工具
    const toggleMcpServerTools = useCallback((serverName: string, enabled: boolean) => {
        setMcpServers(prev => prev.map(server => {
            if (server.name === serverName) {
                const updatedTools = server.tools.map(tool => ({ ...tool, enabled }));
                return {
                    ...server,
                    tools: updatedTools,
                    enabledCount: enabled ? updatedTools.length : 0
                };
            }
            return server;
        }));
    }, []);

    // 添加新的 MCP 服务器
    const addMcpServer = useCallback(async (serverName: string, serverUrl: string) => {
        try {
            // 使用 useMcpServers hook 添加服务器到配置文件
            await addMcpServerConfig({ name: serverName, url: serverUrl });

            // 同时在本地状态中添加新服务器（工具列表会在展开时异步加载）
            const newServer: McpServer = {
                name: serverName,
                url: serverUrl,
                tools: [], // 工具列表为空，会在展开时异步加载
                expanded: false,
                enabledCount: 0
            };

            setMcpServers(prev => [...prev, newServer]);
        } catch (error) {
            console.error('Failed to add MCP server:', error);
            throw error; // 重新抛出错误，让调用方处理
        }
    }, [addMcpServerConfig]);

    // 删除 MCP 服务器
    const deleteMcpServer = useCallback(async (serverName: string) => {
        try {
            // 使用 useMcpServers hook 从配置文件中删除服务器
            await deleteMcpServerConfig(serverName);

            // 同时从本地状态中删除服务器
            setMcpServers(prev => prev.filter(server => server.name !== serverName));

            // 同时清理对应的工具配置
            const newConfig = { ...mcpToolsConfig };
            delete newConfig[serverName];
            setMcpToolsConfig(newConfig);
        } catch (error) {
            console.error('Failed to delete MCP server:', error);
            throw error;
        }
    }, [deleteMcpServerConfig, mcpToolsConfig, setMcpToolsConfig]);

    const stop = useCallback(() => {
        if (controller.current) {
            controller.current.abort();
            controller.current = null;
        }
        setLoading(false);
        setMessages((messages) => {
            const message = messages[messages.length - 1];
            if (message && message.loading) {
                message.loading = false;
            }
        });
    }, [setMessages]);

    const send = useCallback(async (query: string) => {
        if (query) {
            if (controller.current) {
                controller.current.abort();
            }
            controller.current = new AbortController();
            setLoading(true);

            // 直接从 context 中获取工具配置
            const toolsConfig = getEnabledToolsStatus(builtinTools, mcpServers);

            const input = {
                query,
                context: filename ? {
                    filename
                } : undefined,
                ...toolsConfig  // 直接展开 tools 和 mcp 字段
            };

            setMessages((messages) => {
                messages.push(
                    {
                        input: input,
                        output: [],
                        loading: true,
                    },
                );
            });

            try {
                const data: Record<string, any> = {
                    input,
                    conversation: conversationRef.current,
                };

                if (typeof authorized === 'string') {
                    data.token = authorized;
                }

                const requestConfig: RequestConfig = {
                    method: 'post',
                    url: `${assistant.base}/chat`,
                    data,
                    signal: controller.current.signal,
                    onMessage: (message) => {
                        if (message.data) {
                            if (message.data != '[DONE]') {
                                try {
                                    const event = JSON.parse(message.data);
                                    if (event.conversation) {
                                        conversationRef.current = event.conversation;
                                        setConversationId(event.conversation);
                                    } else {
                                        setMessages((messages) => {
                                            const message = messages[messages.length - 1];
                                            if (message.output) {
                                                if (event.chunks) {

                                                    const chunkIndex: number = event.chunks.index;
                                                    if (!message.output[chunkIndex]) {
                                                        message.output[chunkIndex] = {
                                                            content: '',
                                                        };
                                                    }
                                                    if (event.chunks.error) {
                                                        message.output[chunkIndex].error = event.chunks.error;
                                                    } else if (event.chunks.tools) {
                                                        if (!message.output[chunkIndex].tools) {
                                                            message.output[chunkIndex].tools = [];
                                                        }
                                                        const toolIndex = event.chunks.tools.index;
                                                        if ('response' in event.chunks.tools) {
                                                            message.output[chunkIndex].tools[toolIndex].response = event.chunks.tools.response;
                                                            message.output[chunkIndex].tools[toolIndex].error = event.chunks.tools.error;
                                                            message.output[chunkIndex].tools[toolIndex].content = event.chunks.tools.content;
                                                        } else {
                                                            message.output[chunkIndex].tools[toolIndex] = {
                                                                name: event.chunks.tools.name,
                                                                title: event.chunks.tools.title,
                                                                arguments: event.chunks.tools.arguments
                                                            };
                                                        }
                                                    } else if (event.chunks.content) {
                                                        if (typeof event.chunks.content === 'object') {
                                                            if (!Array.isArray(message.output[chunkIndex].content)) {
                                                                message.output[chunkIndex].content = [];
                                                            }
                                                            const contentIndex: number = event.chunks.content.index;
                                                            const contentValue = event.chunks.content.value;

                                                            if (typeof contentValue === 'string') {
                                                                if (!message.output[chunkIndex].content[contentIndex]) {
                                                                    message.output[chunkIndex].content[contentIndex] = '';
                                                                }
                                                                message.output[chunkIndex].content[contentIndex] += contentValue;
                                                            } else {
                                                                message.output[chunkIndex].content[contentIndex] = contentValue;
                                                            }
                                                        } else {
                                                            message.output[chunkIndex].content += event.chunks.content;
                                                        }
                                                    }
                                                } else if (event.id) {
                                                    message.id = event.id;
                                                }
                                            }
                                        });
                                    }
                                } catch (e) {
                                    console.error(e);
                                }
                            } else {
                                setMessages((messages) => {
                                    const message = messages[messages.length - 1];
                                    message.loading = false;
                                });
                            }
                        }
                    },
                };

                await request(requestConfig);
            } catch (e) {
                if (isCancel(e)) {
                    setMessages((messages) => {
                        const message = messages[messages.length - 1];
                        message.loading = false;
                    });
                } else {
                    let errors = '未知错误';
                    if (isRequestError(e)) {
                        if (e.response?.status == 401) {
                            errors = '未授权或授权已过期，请刷新页面后重试';
                        } else {
                            if (typeof e.errors === 'string') {
                                errors = e.errors;
                            } else {
                                errors = Object.values(e.errors).join('\n');
                            }
                        }
                    }
                    setMessages((messages) => {
                        const message = messages[messages.length - 1];
                        if (message.output) {
                            if (message.output.length === 0) {
                                message.output = [{
                                    content: `[${errors}]`,
                                    tools: []
                                }];
                            } else {
                                message.output[message.output.length - 1].content = `[${errors}]`;
                            }
                        }
                        message.loading = false;
                    });
                }
            }

            setLoading(false);
        }
    }, [filename, authorized, builtinTools, mcpServers]);

    const reset = useCallback((conversation?: Conversation) => {
        if (!loading) {
            setConversationId(conversation?.id);
            setMessages(getInitialMessages(conversation));
        }
    }, [loading, getInitialMessages]);

    return <Context.Provider value={{
        filename,
        range: undefined, // 这里可以根据需要设置当前的range状态
        conversationId,
        setConversationId,
        messages,
        setMessages,
        loading,
        setLoading,
        send,
        stop,
        reset,
        windowState,
        setWindowState,
        authorized,
        // 工具配置相关
        builtinTools,
        mcpServers,
        toggleBuiltinTool,
        toggleAllBuiltinTools,
        toggleMcpServer,
        toggleMcpTool,
        toggleMcpServerTools,
        addMcpServer,
        deleteMcpServer,
        saveToolsConfig,
        loadToolsConfig
    }}>
        {children}
    </Context.Provider>;
}

export function useContext() {
    const context = useRcContext(Context);
    if (!context) {
        throw new Error('useContext must be used within a Provider');
    }
    return context;
}
