import { createStorageSlot } from '@topwrite/common';

export interface BuiltinTool {
    plugin: string;
    name: string;
    title: string;
    description: string;
    args?: Record<string, any>;
    enabled: boolean;
}

export interface McpTool {
    name: string;
    title: string;
    description: string;
    enabled: boolean;
}

export interface McpServer {
    name: string;
    url?: string;
    tools: McpTool[];
    expanded: boolean;
    enabledCount: number;
}

// localStorage 相关
const builtinToolsSlot = createStorageSlot('topwrite:builtin-tools-config');
const mcpToolsSlot = createStorageSlot('topwrite:mcp-tools-config');

// 保存内置工具配置到 localStorage
export function saveBuiltinToolsConfig(tools: BuiltinTool[]) {
    const config = tools.reduce((acc, tool) => {
        acc[tool.name] = tool.enabled;
        return acc;
    }, {} as Record<string, boolean>);
    builtinToolsSlot.set(JSON.stringify(config));
}

// 从 localStorage 加载内置工具配置
export function loadBuiltinToolsConfig(): Record<string, boolean> {
    const config = builtinToolsSlot.get();
    return config ? JSON.parse(config) : {};
}

// 保存 MCP 工具配置到 localStorage
export function saveMcpToolsConfig(servers: McpServer[]) {
    const config = servers.reduce((acc, server) => {
        acc[server.name] = server.tools.reduce((toolAcc, tool) => {
            toolAcc[tool.name] = tool.enabled;
            return toolAcc;
        }, {} as Record<string, boolean>);
        return acc;
    }, {} as Record<string, Record<string, boolean>>);
    mcpToolsSlot.set(JSON.stringify(config));
}

// 从 localStorage 加载 MCP 工具配置
export function loadMcpToolsConfig(): Record<string, Record<string, boolean>> {
    const config = mcpToolsSlot.get();
    return config ? JSON.parse(config) : {};
}

// 保存 MCP 服务器列表到文件 
export async function saveMcpServersToFile(servers: Array<{ name: string; url?: string; tools: Array<{ name: string; title: string; description: string }> }>) {
    try {
        const data = {
            servers: servers.map(server => ({
                name: server.name,
                url: server.url,
                tools: server.tools.map(tool => ({
                    name: tool.name,
                    title: tool.title,
                    description: tool.description
                }))
            }))
        };
        
        // 暂时保存到 localStorage 作为备用方案
        // TODO: 实现真正的文件保存 API
        const mcpServersSlot = createStorageSlot('topwrite:mcp-servers');
        mcpServersSlot.set(JSON.stringify(data));
        
        console.log('MCP servers configuration saved:', data);
    } catch (error) {
        console.error('Error saving MCP servers:', error);
        throw error;
    }
}

// 从文件加载 MCP 服务器列表
export async function loadMcpServersFromFile(): Promise<Array<{ name: string; url?: string; tools: Array<{ name: string; title: string; description: string }> }>> {
    try {
        // 暂时从 localStorage 加载作为备用方案
        // TODO: 实现真正的文件加载 API
        const mcpServersSlot = createStorageSlot('topwrite:mcp-servers');
        const data = mcpServersSlot.get();
        
        if (data) {
            const parsed = JSON.parse(data);
            return parsed.servers || [];
        }
        return [];
    } catch (error) {
        console.error('Error loading MCP servers:', error);
        return [];
    }
}

// 获取已启用的工具状态，用于发送消息
export function getEnabledToolsStatus(builtinTools: BuiltinTool[], mcpServers: McpServer[]) {
    // 内置工具格式: {plugin: 'xx', name: 'bb'}
    const enabledBuiltinTools = builtinTools.filter(tool => tool.enabled).map(tool => ({
        plugin: tool.plugin,
        name: tool.name
    }));
    
    // MCP 工具格式: {name: 'aa', url: 'bb', allowed: ['aa']} 或 {name: 'aa', url: 'bb'}
    const enabledMcpServers = mcpServers.reduce((acc, server) => {
        const enabledTools = server.tools.filter(tool => tool.enabled);
        const allToolsEnabled = enabledTools.length === server.tools.length;
        
        if (enabledTools.length > 0) {
            const mcpServer: any = {
                name: server.name,
                url: server.url
            };
            
            // 如果不是全部工具都启用，则添加 allowed 字段
            if (!allToolsEnabled) {
                mcpServer.allowed = enabledTools.map(tool => tool.name);
            }
            
            acc.push(mcpServer);
        }
        return acc;
    }, [] as Array<{ name: string; url?: string; allowed?: string[] }>);
    
    return {
        tools: enabledBuiltinTools,
        mcp: enabledMcpServers
    };
}
